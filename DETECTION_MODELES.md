# 🤖 Système de Détection Automatique des Modèles OpenRouter

## 📋 Vue d'ensemble

Le Studio de Workflow Agentique intègre un système avancé de détection automatique des modèles OpenRouter qui optimise le raisonnement agentique en utilisant différents modèles pour chaque phase de réflexion.

## 🎯 Objectifs

### **Raisonnement Agentique Optimisé**
- **Variation des modèles** : Chaque étape du workflow utilise un modèle différent pour maximiser l'efficacité
- **Rotation intelligente** : Sélection automatique du meilleur modèle disponible pour chaque tâche
- **Fallback automatique** : Basculement vers d'autres modèles en cas d'échec

### **Mise à jour Hebdomadaire Automatique**
- **Détection de nouveaux modèles** : Vérification automatique des nouveaux modèles gratuits
- **Cache intelligent** : Stockage local pour éviter les appels API répétés
- **Logs détaillés** : Suivi des nouveaux modèles détectés

## 🔧 Architecture Technique

### **1. Service de Détection (`ModelDetectionService`)**
```typescript
// Singleton pour la gestion centralisée
const modelDetectionService = ModelDetectionService.getInstance();

// Mise à jour automatique si nécessaire
await modelDetectionService.updateModelsIfNeeded();

// Vérification de disponibilité
const isAvailable = modelDetectionService.isModelAvailable(modelId);
```

### **2. Configuration des Modèles (`constants.ts`)**
```typescript
export const OPENROUTER_MODELS = {
  "analyse": [
    "deepseek/deepseek-r1:free",
    "google/gemma-3-27b-it:free", 
    "mistralai/mistral-nemo:free",
    // ... autres modèles
  ],
  // ... autres tâches
};
```

### **3. Rotation Intelligente (`geminiService.ts`)**
```typescript
// Sélection avec exclusion des modèles échoués
function selectModelForTask(task: Step['task'], excludeModels: string[] = []): string {
    const availableModels = modelsForTask.filter(model => 
        !excludeModels.includes(model) && 
        modelDetectionService.isModelAvailable(model)
    );
    // Sélection aléatoire pour varier les approches
    return availableModels[Math.floor(Math.random() * availableModels.length)];
}
```

## 🚀 Fonctionnalités Clés

### **Détection Automatique**
- ✅ **API OpenRouter** : Récupération de la liste complète des modèles
- ✅ **Filtrage gratuit** : Identification automatique des modèles `:free`
- ✅ **Validation prix** : Vérification des tarifs (0$ = gratuit)
- ✅ **Cache persistant** : Stockage localStorage pour les performances

### **Rotation des Modèles**
- ✅ **Sélection intelligente** : Choix optimal par type de tâche
- ✅ **Exclusion d'échecs** : Évitement des modèles défaillants
- ✅ **Retry automatique** : Tentatives multiples avec délais
- ✅ **Logs détaillés** : Traçabilité complète des sélections

### **Monitoring en Temps Réel**
- ✅ **Composant ModelMonitor** : Interface de suivi
- ✅ **Statistiques live** : Nombre de modèles disponibles
- ✅ **Mise à jour forcée** : Bouton de refresh manuel
- ✅ **Alertes visuelles** : Notifications en cas de problème

## 📊 Types de Tâches et Modèles

### **Analyse** 🔍
- **Modèles optimisés** : DeepSeek R1, Gemma 3 27B
- **Usage** : Définition problème, diagnostic, exploration
- **Caractéristiques** : Précision analytique, raisonnement structuré

### **Génération** ✨
- **Modèles optimisés** : DeepSeek R1, Llama 3.3 70B
- **Usage** : Création solutions, prototypage
- **Caractéristiques** : Créativité, innovation, génération de contenu

### **Validation** ✅
- **Modèles optimisés** : Gemma 3 27B, DeepSeek R1
- **Usage** : Vérification éthique, simulation prédictive
- **Caractéristiques** : Rigueur, validation, contrôle qualité

### **Synthèse** 📝
- **Modèles optimisés** : Llama 3.3 70B, DeepSeek R1
- **Usage** : Capitalisation, communication, plan d'action
- **Caractéristiques** : Synthèse, clarté, structuration

## ⚙️ Configuration

### **Variables d'Environnement**
```bash
# Fichier .env.local
VITE_API_KEY=sk-or-v1-your-openrouter-key-here
```

### **Paramètres de Détection**
```typescript
export const MODEL_DETECTION_CONFIG = {
  UPDATE_INTERVAL: 7 * 24 * 60 * 60 * 1000, // 1 semaine
  MAX_RETRIES: 3,                            // 3 tentatives max
  RETRY_DELAY: 1000,                         // 1 seconde entre tentatives
};
```

## 🔄 Flux de Fonctionnement

### **1. Initialisation**
1. Chargement cache localStorage
2. Vérification âge des données (< 1 semaine)
3. Mise à jour si nécessaire

### **2. Sélection de Modèle**
1. Récupération modèles pour la tâche
2. Filtrage disponibilité + exclusions
3. Sélection aléatoire intelligente
4. Logging de la sélection

### **3. Exécution avec Fallback**
1. Tentative avec modèle sélectionné
2. En cas d'échec : exclusion + retry
3. Rotation automatique jusqu'à succès
4. Logging détaillé des résultats

### **4. Monitoring**
1. Affichage statistiques temps réel
2. Alertes si aucun modèle gratuit
3. Bouton mise à jour forcée
4. Historique des mises à jour

## 🎯 Avantages du Système

### **Pour le Raisonnement Agentique**
- **Diversité cognitive** : Chaque modèle apporte sa spécialité
- **Robustesse** : Pas de point de défaillance unique
- **Optimisation** : Meilleur modèle pour chaque tâche
- **Évolutivité** : Intégration automatique nouveaux modèles

### **Pour la Maintenance**
- **Automatisation** : Zéro intervention manuelle
- **Monitoring** : Visibilité complète du système
- **Logs** : Traçabilité pour debugging
- **Performance** : Cache intelligent pour rapidité

## 🚨 Gestion d'Erreurs

### **Échecs de Modèles**
- Rotation automatique vers modèles alternatifs
- Logs détaillés pour diagnostic
- Messages utilisateur informatifs

### **Problèmes de Réseau**
- Utilisation cache local en fallback
- Retry avec délais progressifs
- Alertes visuelles appropriées

### **Modèles Indisponibles**
- Détection automatique indisponibilité
- Exclusion temporaire des listes
- Notification utilisateur si critique

---

**🎉 Résultat** : Un système de raisonnement agentique ultra-efficace qui s'adapte automatiquement aux nouveaux modèles OpenRouter et optimise chaque phase de réflexion !
