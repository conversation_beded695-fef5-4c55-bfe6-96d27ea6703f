import { OPENROUTER_MODELS_API, MODEL_DETECTION_CONFIG, OPENROUTER_MODELS } from '../constants';
import type { Step } from '../types';

// Interface pour les modèles OpenRouter
interface OpenRouterModel {
    id: string;
    name: string;
    pricing: {
        prompt: string;
        completion: string;
    };
    context_length: number;
    per_request_limits?: {
        prompt_tokens?: string;
        completion_tokens?: string;
    };
}

interface ModelStats {
    totalModels: number;
    freeModels: number;
    lastUpdate: number;
    availableByTask: Record<string, string[]>;
}

/**
 * Service de détection automatique des modèles OpenRouter
 * Gère la mise à jour hebdomadaire et la rotation intelligente des modèles
 */
export class ModelDetectionService {
    private static instance: ModelDetectionService;
    private modelsCache: OpenRouterModel[] = [];
    private lastUpdate = 0;
    private updatePromise: Promise<void> | null = null;

    private constructor() {
        this.loadFromLocalStorage();
    }

    public static getInstance(): ModelDetectionService {
        if (!ModelDetectionService.instance) {
            ModelDetectionService.instance = new ModelDetectionService();
        }
        return ModelDetectionService.instance;
    }

    /**
     * Charge la cache depuis le localStorage
     */
    private loadFromLocalStorage(): void {
        try {
            const cachedModels = localStorage.getItem(MODEL_DETECTION_CONFIG.CACHE_KEY);
            const lastUpdateStr = localStorage.getItem(MODEL_DETECTION_CONFIG.LAST_UPDATE_KEY);
            
            if (cachedModels && lastUpdateStr) {
                this.modelsCache = JSON.parse(cachedModels);
                this.lastUpdate = parseInt(lastUpdateStr, 10);
                console.log(`📦 Cache des modèles chargée: ${this.modelsCache.length} modèles`);
            }
        } catch (error) {
            console.warn('Erreur lors du chargement de la cache des modèles:', error);
        }
    }

    /**
     * Sauvegarde la cache dans le localStorage
     */
    private saveToLocalStorage(): void {
        try {
            localStorage.setItem(MODEL_DETECTION_CONFIG.CACHE_KEY, JSON.stringify(this.modelsCache));
            localStorage.setItem(MODEL_DETECTION_CONFIG.LAST_UPDATE_KEY, this.lastUpdate.toString());
        } catch (error) {
            console.warn('Erreur lors de la sauvegarde de la cache des modèles:', error);
        }
    }

    /**
     * Récupère la liste des modèles depuis l'API OpenRouter
     */
    private async fetchModelsFromAPI(): Promise<OpenRouterModel[]> {
        try {
            console.log('🔍 Récupération des modèles depuis OpenRouter API...');
            const response = await fetch(OPENROUTER_MODELS_API);
            
            if (!response.ok) {
                throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            const models = data.data || [];
            
            console.log(`✅ ${models.length} modèles récupérés depuis l'API`);
            return models;
        } catch (error) {
            console.error('❌ Erreur lors de la récupération des modèles:', error);
            return [];
        }
    }

    /**
     * Vérifie si un modèle est gratuit
     */
    private isModelFree(model: OpenRouterModel): boolean {
        // Vérifier si le modèle contient ":free" dans l'ID
        if (model.id.includes(':free')) {
            return true;
        }
        
        // Vérifier si les prix sont à 0
        const promptPrice = parseFloat(model.pricing.prompt || '0');
        const completionPrice = parseFloat(model.pricing.completion || '0');
        
        return promptPrice === 0 && completionPrice === 0;
    }

    /**
     * Met à jour la cache des modèles si nécessaire
     */
    public async updateModelsIfNeeded(): Promise<void> {
        const now = Date.now();
        const shouldUpdate = this.modelsCache.length === 0 || 
                           (now - this.lastUpdate) > MODEL_DETECTION_CONFIG.UPDATE_INTERVAL;

        if (!shouldUpdate) {
            return;
        }

        // Éviter les mises à jour simultanées
        if (this.updatePromise) {
            return this.updatePromise;
        }

        this.updatePromise = this.performUpdate();
        await this.updatePromise;
        this.updatePromise = null;
    }

    /**
     * Effectue la mise à jour des modèles
     */
    private async performUpdate(): Promise<void> {
        try {
            const models = await this.fetchModelsFromAPI();
            
            if (models.length > 0) {
                this.modelsCache = models;
                this.lastUpdate = Date.now();
                this.saveToLocalStorage();
                
                const freeModels = models.filter(m => this.isModelFree(m));
                console.log(`🎉 Cache mise à jour: ${models.length} modèles total, ${freeModels.length} gratuits`);
                
                // Log des nouveaux modèles gratuits détectés
                this.logNewFreeModels(freeModels);
            }
        } catch (error) {
            console.error('❌ Erreur lors de la mise à jour des modèles:', error);
        }
    }

    /**
     * Log des nouveaux modèles gratuits détectés
     */
    private logNewFreeModels(freeModels: OpenRouterModel[]): void {
        const currentFreeIds = new Set(Object.values(OPENROUTER_MODELS).flat());
        const newFreeModels = freeModels.filter(m => !currentFreeIds.has(m.id));
        
        if (newFreeModels.length > 0) {
            console.log('🆕 Nouveaux modèles gratuits détectés:');
            newFreeModels.forEach(model => {
                console.log(`  - ${model.id} (${model.name})`);
            });
        }
    }

    /**
     * Vérifie si un modèle spécifique est disponible et gratuit
     */
    public isModelAvailable(modelId: string): boolean {
        if (this.modelsCache.length === 0) {
            return true; // Fallback si pas de cache
        }
        
        const model = this.modelsCache.find(m => m.id === modelId);
        return model ? this.isModelFree(model) : false;
    }

    /**
     * Obtient les statistiques des modèles
     */
    public getModelStats(): ModelStats {
        const freeModels = this.modelsCache.filter(m => this.isModelFree(m));
        
        const availableByTask: Record<string, string[]> = {};
        Object.entries(OPENROUTER_MODELS).forEach(([task, models]) => {
            availableByTask[task] = models.filter(modelId => this.isModelAvailable(modelId));
        });
        
        return {
            totalModels: this.modelsCache.length,
            freeModels: freeModels.length,
            lastUpdate: this.lastUpdate,
            availableByTask
        };
    }

    /**
     * Obtient tous les modèles gratuits disponibles
     */
    public getFreeModels(): OpenRouterModel[] {
        return this.modelsCache.filter(m => this.isModelFree(m));
    }

    /**
     * Force une mise à jour immédiate des modèles
     */
    public async forceUpdate(): Promise<void> {
        this.lastUpdate = 0; // Force la mise à jour
        await this.updateModelsIfNeeded();
    }
}

// Instance singleton
export const modelDetectionService = ModelDetectionService.getInstance();
