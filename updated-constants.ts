// Modèles OpenRouter gratuits 2025 - Mise à jour automatique
export const OPENROUTER_MODELS = {
  "analyse": [
    "openai/gpt-oss-20b:free",
    "z-ai/glm-4.5-air:free",
    "cognitivecomputations/dolphin-mistral-24b-venice-edition:free",
    "google/gemma-3n-e2b-it:free",
    "tngtech/deepseek-r1t2-chimera:free",
    "moonshotai/kimi-dev-72b:free",
    "deepseek/deepseek-r1-0528-qwen3-8b:free",
    "deepseek/deepseek-r1-0528:free",
    "qwen/qwen3-4b:free",
    "qwen/qwen3-14b:free",
    "qwen/qwen3-235b-a22b:free",
    "tngtech/deepseek-r1t-chimera:free",
    "arliai/qwq-32b-arliai-rpr-v1:free",
    "moonshotai/kimi-vl-a3b-thinking:free",
    "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
    "featherless/qwerky-72b:free",
    "mistralai/mistral-small-3.1-24b-instruct:free",
    "google/gemma-3-4b-it:free",
    "google/gemma-3-12b-it:free",
    "google/gemma-3-27b-it:free",
    "qwen/qwq-32b:free",
    "nousresearch/deephermes-3-llama-3-8b-preview:free",
    "mistralai/mistral-small-24b-instruct-2501:free",
    "deepseek/deepseek-r1-distill-qwen-14b:free",
    "deepseek/deepseek-r1-distill-llama-70b:free",
    "deepseek/deepseek-r1:free",
    "meta-llama/llama-3.2-11b-vision-instruct:free",
    "meta-llama/llama-3.2-3b-instruct:free",
  ],
  "génération": [
    "qwen/qwen3-coder:free",
    "moonshotai/kimi-k2:free",
    "tencent/hunyuan-a13b-instruct:free",
    "mistralai/mistral-small-3.2-24b-instruct:free",
    "sarvamai/sarvam-m:free",
    "mistralai/devstral-small-2505:free",
    "qwen/qwen3-30b-a3b:free",
    "qwen/qwen3-8b:free",
    "microsoft/mai-ds-r1:free",
    "agentica-org/deepcoder-14b-preview:free",
    "google/gemini-2.5-pro-exp-03-25",
    "qwen/qwen2.5-vl-32b-instruct:free",
    "rekaai/reka-flash-3:free",
    "cognitivecomputations/dolphin3.0-r1-mistral-24b:free",
    "cognitivecomputations/dolphin3.0-mistral-24b:free",
    "google/gemini-2.0-flash-exp:free",
    "qwen/qwen-2.5-coder-32b-instruct:free",
    "qwen/qwen-2.5-72b-instruct:free",
    "mistralai/mistral-nemo:free",
    "google/gemma-2-9b-it:free",
  ],
  "validation": [
    "google/gemma-3n-e4b-it:free",
    "qwen/qwen2.5-vl-72b-instruct:free",
  ],
  "synthèse": [
    "shisa-ai/shisa-v2-llama3.3-70b:free",
    "deepseek/deepseek-chat-v3-0324:free",
    "meta-llama/llama-3.3-70b-instruct:free",
    "meta-llama/llama-3.1-405b-instruct:free",
    "mistralai/mistral-7b-instruct:free",
  ],
};
