// Script pour analyser les modèles OpenRouter et identifier les modèles gratuits
// Cisco - Mise à jour des modèles pour l'application Agent Engineering

import fs from 'fs';
import https from 'https';

// Fonction pour récupérer les modèles depuis l'API OpenRouter
async function fetchModels() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'openrouter.ai',
            path: '/api/v1/models',
            method: 'GET',
            headers: {
                'User-Agent': 'Agent-Engineering-App/1.0'
            }
        };

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve(jsonData);
                } catch (error) {
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.end();
    });
}

// Fonction pour déterminer si un modèle est gratuit
function isModelFree(model) {
    const pricing = model.pricing;
    
    // Vérifier si le modèle a ":free" dans son ID
    if (model.id.includes(':free')) {
        return true;
    }
    
    // Vérifier si les prix prompt et completion sont à 0
    if (pricing && 
        (pricing.prompt === "0" || pricing.prompt === 0) && 
        (pricing.completion === "0" || pricing.completion === 0)) {
        return true;
    }
    
    return false;
}

// Fonction pour catégoriser un modèle selon sa spécialité
function categorizeModel(model) {
    const name = model.name.toLowerCase();
    const description = model.description.toLowerCase();
    const id = model.id.toLowerCase();
    
    // Modèles de code
    if (name.includes('code') || name.includes('coder') || 
        description.includes('coding') || description.includes('code generation') ||
        id.includes('code')) {
        return 'génération'; // Les modèles de code sont excellents pour la génération
    }
    
    // Modèles de raisonnement
    if (name.includes('reasoning') || name.includes('thinking') || 
        description.includes('reasoning') || description.includes('thinking') ||
        name.includes('r1') || id.includes('thinking')) {
        return 'analyse'; // Les modèles de raisonnement pour l'analyse
    }
    
    // Modèles multimodaux (vision)
    if (description.includes('vision') || description.includes('image') || 
        description.includes('multimodal') || name.includes('vision') ||
        model.architecture?.input_modalities?.includes('image')) {
        return 'validation'; // Les modèles multimodaux pour la validation
    }
    
    // Modèles de chat/conversation
    if (name.includes('chat') || name.includes('instruct') || 
        description.includes('conversation') || description.includes('chat')) {
        return 'synthèse'; // Les modèles de chat pour la synthèse
    }
    
    // Modèles légers/rapides
    if (name.includes('mini') || name.includes('small') || name.includes('tiny') ||
        name.includes('air') || name.includes('nano')) {
        return 'analyse'; // Les modèles légers pour l'analyse rapide
    }
    
    // Modèles larges/puissants
    if (name.includes('large') || name.includes('opus') || name.includes('max') ||
        description.includes('flagship') || description.includes('most powerful')) {
        return 'validation'; // Les modèles puissants pour la validation
    }
    
    // Par défaut, assigner selon l'ordre alphabétique pour équilibrer
    const firstLetter = model.id.charAt(0).toLowerCase();
    if (firstLetter <= 'f') return 'analyse';
    if (firstLetter <= 'n') return 'génération';
    if (firstLetter <= 't') return 'validation';
    return 'synthèse';
}

// Fonction principale pour analyser les modèles
async function analyzeModels() {
    console.log('🔍 Récupération des modèles depuis OpenRouter API...\n');

    try {
        const modelsData = await fetchModels();

        console.log('✅ Modèles récupérés avec succès!\n');
        console.log('🔍 Analyse des modèles OpenRouter...\n');

        const freeModels = [];
        const paidModels = [];

        modelsData.data.forEach(model => {
            if (isModelFree(model)) {
                freeModels.push(model);
            } else {
                paidModels.push(model);
            }
        });

        console.log(`📊 Résultats de l'analyse :`);
        console.log(`   • Total des modèles : ${modelsData.data.length}`);
        console.log(`   • Modèles gratuits : ${freeModels.length}`);
        console.log(`   • Modèles payants : ${paidModels.length}\n`);

        return { freeModels, paidModels, totalModels: modelsData.data.length };
    } catch (error) {
        console.error('❌ Erreur lors de la récupération des modèles:', error);
        throw error;
    }
}

// Fonction pour traiter et organiser les modèles gratuits
function processModels(freeModels) {
    // Organiser les modèles gratuits par catégorie
    const categorizedFreeModels = {
        'analyse': [],
        'génération': [],
        'validation': [],
        'synthèse': []
    };

    freeModels.forEach(model => {
        const category = categorizeModel(model);
        categorizedFreeModels[category].push(model.id);
    });

    console.log('🆓 Modèles gratuits par catégorie :');
    Object.keys(categorizedFreeModels).forEach(category => {
        console.log(`\n${category.toUpperCase()} (${categorizedFreeModels[category].length} modèles) :`);
        categorizedFreeModels[category].forEach(modelId => {
            console.log(`   • ${modelId}`);
        });
    });

    return categorizedFreeModels;
}

// Générer le code TypeScript pour constants.ts
function generateConstantsCode(categorizedFreeModels) {
    let code = `// Modèles OpenRouter gratuits 2025 - Mise à jour automatique\n`;
    code += `export const OPENROUTER_MODELS = {\n`;

    Object.keys(categorizedFreeModels).forEach(category => {
        code += `  "${category}": [\n`;
        categorizedFreeModels[category].forEach(modelId => {
            code += `    "${modelId}",\n`;
        });
        code += `  ],\n`;
    });

    code += `};\n`;
    return code;
}

// Fonction principale
async function main() {
    try {
        const { freeModels, paidModels, totalModels } = await analyzeModels();
        const categorizedFreeModels = processModels(freeModels);

        // Sauvegarder le code généré
        const constantsCode = generateConstantsCode(categorizedFreeModels);
        fs.writeFileSync('updated-constants.ts', constantsCode);

        console.log('\n✅ Code généré et sauvegardé dans "updated-constants.ts"');
        console.log('\n🎯 Prochaines étapes :');
        console.log('   1. Vérifier le fichier updated-constants.ts');
        console.log('   2. Remplacer la section OPENROUTER_MODELS dans constants.ts');
        console.log('   3. Tester l\'application avec les nouveaux modèles');

        // Afficher quelques statistiques supplémentaires
        console.log('\n📈 Statistiques détaillées :');
        console.log(`   • Modèles d'analyse : ${categorizedFreeModels.analyse.length}`);
        console.log(`   • Modèles de génération : ${categorizedFreeModels.génération.length}`);
        console.log(`   • Modèles de validation : ${categorizedFreeModels.validation.length}`);
        console.log(`   • Modèles de synthèse : ${categorizedFreeModels.synthèse.length}`);

    } catch (error) {
        console.error('❌ Erreur lors de l\'exécution:', error);
        process.exit(1);
    }
}

// Exécuter le script
main();
